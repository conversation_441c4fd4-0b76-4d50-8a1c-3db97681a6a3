<!-- <PERSON><PERSON> Admin Panel -->
<div class="container-fluid mt-4" [ngClass]="{'fade-in': !isLoading}">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Cache verileri yükleniyor...">
  </app-loading-spinner>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="main-content">

    <!-- Page Header with Help Button -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <div class="d-flex align-items-center gap-2">
              <h5 class="mb-0">
                <i class="fas fa-database me-2"></i>
                Cache Yönetim Paneli
              </h5>
              <app-help-button
                guideId="cache-admin"
                position="inline"
                size="small"
                tooltip="Cache yönetimi hakkında yardım al">
              </app-help-button>
            </div>
            <p class="text-muted mb-0 mt-1">
              Redis cache sisteminin yönetimi ve izlenmesi
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Cache Health Dashboard -->
    <div class="row mb-4" *ngIf="healthInfo">
      <div class="col-md-3 col-sm-6 mb-3">
        <div class="modern-stats-card" [ngClass]="healthInfo.isConnected ? 'bg-success' : 'bg-danger'">
          <div class="modern-stats-icon">
            <i class="fas" [ngClass]="healthInfo.isConnected ? 'fa-check-circle' : 'fa-times-circle'"></i>
          </div>
          <div class="modern-stats-info">
            <h6 class="text-white mb-1">Cache Bağlantısı</h6>
            <h4 class="text-white mb-0">{{ healthInfo.isConnected ? 'Aktif' : 'Pasif' }}</h4>
            <small class="text-light">Redis Server</small>
          </div>
        </div>
      </div>
      <div class="col-md-3 col-sm-6 mb-3">
        <div class="modern-stats-card bg-info">
          <div class="modern-stats-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="modern-stats-info">
            <h6 class="text-white mb-1">Ping Süresi</h6>
            <h4 class="text-white mb-0">{{ healthInfo.pingTime | number:'1.2-2' }}ms</h4>
            <small class="text-light">Bağlantı Hızı</small>
          </div>
        </div>
      </div>
      <div class="col-md-3 col-sm-6 mb-3">
        <div class="modern-stats-card bg-primary">
          <div class="modern-stats-icon">
            <i class="fas fa-tachometer-alt"></i>
          </div>
          <div class="modern-stats-info">
            <h6 class="text-white mb-1">Response Time</h6>
            <h4 class="text-white mb-0">{{ healthInfo.responseTime | number }}ms</h4>
            <small class="text-light">İşlem Hızı</small>
          </div>
        </div>
      </div>
      <div class="col-md-3 col-sm-6 mb-3">
        <div class="modern-stats-card bg-warning">
          <div class="modern-stats-icon">
            <i class="fas fa-server"></i>
          </div>
          <div class="modern-stats-info">
            <h6 class="text-white mb-1">Server Info</h6>
            <h4 class="text-white mb-0">{{ healthInfo.serverInfo || 'N/A' }}</h4>
            <small class="text-light">Redis Version</small>
          </div>
        </div>
      </div>
    </div>

    <!-- Cache Statistics Overview -->
    <div class="row mb-4" *ngIf="statistics">
      <div class="col-md-3 col-sm-6 mb-3">
        <div class="modern-stats-card bg-gradient-primary">
          <div class="modern-stats-icon">
            <i class="fas fa-building"></i>
          </div>
          <div class="modern-stats-info">
            <h6 class="text-white mb-1">Company ID</h6>
            <h4 class="text-white mb-0">{{ statistics.companyId }}</h4>
            <small class="text-light">Aktif Şirket</small>
          </div>
        </div>
      </div>
      <div class="col-md-3 col-sm-6 mb-3">
        <div class="modern-stats-card bg-gradient-success">
          <div class="modern-stats-icon">
            <i class="fas fa-key"></i>
          </div>
          <div class="modern-stats-info">
            <h6 class="text-white mb-1">Toplam Keys</h6>
            <h4 class="text-white mb-0">{{ statistics.totalKeys | number }}</h4>
            <small class="text-light">Cache Anahtarları</small>
          </div>
        </div>
      </div>
      <div class="col-md-3 col-sm-6 mb-3">
        <div class="modern-stats-card bg-gradient-info">
          <div class="modern-stats-icon">
            <i class="fas fa-memory"></i>
          </div>
          <div class="modern-stats-info">
            <h6 class="text-white mb-1">Bellek Kullanımı</h6>
            <h4 class="text-white mb-0">{{ statistics.totalMemoryUsageMB | number:'1.1-1' }} MB</h4>
            <small class="text-light">RAM Usage</small>
          </div>
        </div>
      </div>
      <div class="col-md-3 col-sm-6 mb-3">
        <div class="modern-stats-card bg-gradient-warning">
          <div class="modern-stats-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="modern-stats-info">
            <h6 class="text-white mb-1">Hit Oranı</h6>
            <h4 class="text-white mb-0">{{ getCacheHitRatio() }}%</h4>
            <small class="text-light">Cache Hit Rate</small>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="modern-card mb-4">
      <div class="modern-card-header p-0">
        <div class="tabs-header">
          <div class="tab" [class.active]="activeTab === 'overview'" (click)="setActiveTab('overview')">
            <i class="fas fa-chart-pie"></i>
            Genel Bakış
          </div>
          <div class="tab" [class.active]="activeTab === 'keys'" (click)="setActiveTab('keys')">
            <i class="fas fa-key"></i>
            Cache Keys
          </div>
          <div class="tab" [class.active]="activeTab === 'management'" (click)="setActiveTab('management')">
            <i class="fas fa-cogs"></i>
            Yönetim
          </div>
          <div class="tab" [class.active]="activeTab === 'warmup'" (click)="setActiveTab('warmup')">
            <i class="fas fa-fire"></i>
            Warmup
          </div>
        </div>
      </div>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      <!-- Overview Tab -->
      <div *ngIf="activeTab === 'overview'" class="fade-in">
        <!-- Entity Distribution -->
        <div class="modern-card" *ngIf="statistics?.keysByEntity">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-chart-pie me-2"></i>
              Entity Dağılımı
            </h5>
          </div>
          <div class="modern-card-body">
            <div class="row">
              <div *ngFor="let entity of statistics?.keysByEntity | keyvalue; let i = index" class="col-md-3 col-sm-6 mb-3">
                <div class="modern-card slide-in-left" [style.animation-delay]="(i * 0.1) + 's'">
                  <div class="modern-card-body text-center">
                    <div class="modern-badge modern-badge-primary mb-2">{{ entity.key }}</div>
                    <h4 class="text-primary">{{ entity.value | number }}</h4>
                    <small class="text-muted">cache entries</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Cache Keys Tab -->
      <div *ngIf="activeTab === 'keys'" class="fade-in">
        <!-- Search and Filter -->
        <div class="modern-card mb-4">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-search me-2"></i>
              Cache Key Arama ve Filtreleme
            </h5>
          </div>
          <div class="modern-card-body">
            <div class="row">
              <div class="col-md-8">
                <div class="input-group">
                  <span class="input-group-text"><i class="fas fa-search"></i></span>
                  <input type="text" class="modern-form-control" [(ngModel)]="searchPattern"
                         placeholder="Pattern girin (örn: member:*, payment:123, *)">
                  <button class="modern-btn modern-btn-primary" (click)="searchKeys()">
                    <i class="fas fa-search me-1"></i>
                    Ara
                  </button>
                </div>
              </div>
              <div class="col-md-4">
                <select class="modern-form-control" [(ngModel)]="selectedPattern" (change)="applyPredefinedPattern(selectedPattern)">
                  <option value="">Hazır Pattern Seç</option>
                  <option *ngFor="let pattern of getPredefinedPatternKeys()" [value]="pattern">{{ pattern }}</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Cache Keys Results -->
        <div class="modern-card" *ngIf="cacheKeys">
          <div class="modern-card-header">
            <div class="d-flex justify-content-between align-items-center">
              <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                Cache Keys Listesi
              </h5>
              <div class="d-flex gap-2">
                <span class="modern-badge modern-badge-info">{{ cacheKeys.pagination.totalCount }} toplam</span>
                <button class="modern-btn modern-btn-outline-primary modern-btn-sm" (click)="loadCacheKeys()">
                  <i class="fas fa-sync-alt me-1"></i>
                  Yenile
                </button>
              </div>
            </div>
          </div>
          <div class="modern-card-body">
            <!-- Pagination Controls -->
            <div class="d-flex justify-content-between align-items-center mb-3">
              <div>
                <select class="modern-form-control modern-form-control-sm" [(ngModel)]="pageSize" (change)="changePageSize(pageSize)">
                  <option [value]="25">25 / sayfa</option>
                  <option [value]="50">50 / sayfa</option>
                  <option [value]="100">100 / sayfa</option>
                </select>
              </div>
              <div class="d-flex gap-1">
                <button class="modern-btn modern-btn-outline-secondary modern-btn-sm"
                        [disabled]="currentPage <= 1"
                        (click)="changePage(currentPage - 1)">
                  <i class="fas fa-chevron-left"></i>
                </button>
                <span class="modern-badge modern-badge-secondary">
                  {{ currentPage }} / {{ Math.ceil(cacheKeys.pagination.totalCount / pageSize) }}
                </span>
                <button class="modern-btn modern-btn-outline-secondary modern-btn-sm"
                        [disabled]="currentPage >= Math.ceil(cacheKeys.pagination.totalCount / pageSize)"
                        (click)="changePage(currentPage + 1)">
                  <i class="fas fa-chevron-right"></i>
                </button>
              </div>
            </div>

            <!-- Keys Table -->
            <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
              <table class="modern-table table table-hover">
                <thead class="table-dark sticky-top">
                  <tr>
                    <th>Cache Key</th>
                    <th>Entity</th>
                    <th>Boyut</th>
                    <th>TTL</th>
                    <th>Oluşturulma</th>
                    <th>İşlemler</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let key of cacheKeys.keys; let i = index" class="fade-in" [style.animation-delay]="(i * 0.02) + 's'">
                    <td><code class="small text-break">{{ key.key }}</code></td>
                    <td><span class="modern-badge modern-badge-secondary">{{ key.type || 'N/A' }}</span></td>
                    <td><span class="modern-badge modern-badge-info">{{ formatBytes(key.memoryUsage) }}</span></td>
                    <td><span class="modern-badge modern-badge-warning">{{ key.ttl && key.ttl > 0 ? (key.ttl + 's') : 'Permanent' }}</span></td>
                    <td class="small">{{ formatDateTime(key.createdAt) }}</td>
                    <td>
                      <div class="d-flex gap-1">
                        <button class="modern-btn modern-btn-outline-info modern-btn-sm"
                                (click)="getCacheKeyValue()"
                                title="Değeri Görüntüle">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button class="modern-btn modern-btn-outline-danger modern-btn-sm"
                                (click)="deleteCacheKey(key.key)"
                                title="Sil">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Empty State -->
            <div *ngIf="cacheKeys.keys.length === 0" class="text-center py-5">
              <i class="fas fa-search fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">Cache key bulunamadı</h5>
              <p class="text-muted">Arama kriterlerinizi değiştirip tekrar deneyin.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Management Tab -->
      <div *ngIf="activeTab === 'management'" class="fade-in">
        <!-- Quick Actions -->
        <div class="row mb-4">
          <div class="col-md-4 mb-3">
            <div class="modern-card">
              <div class="modern-card-header">
                <h6 class="mb-0">
                  <i class="fas fa-trash-alt me-2 text-danger"></i>
                  Tüm Cache Temizle
                </h6>
              </div>
              <div class="modern-card-body">
                <p class="text-muted mb-3">Sistemdeki tüm cache verilerini temizler.</p>
                <button class="modern-btn modern-btn-danger w-100" (click)="clearAllCache()">
                  <i class="fas fa-trash-alt me-1"></i>
                  Tümünü Temizle
                </button>
              </div>
            </div>
          </div>

          <div class="col-md-4 mb-3">
            <div class="modern-card">
              <div class="modern-card-header">
                <h6 class="mb-0">
                  <i class="fas fa-broom me-2 text-warning"></i>
                  Company Cache Temizle
                </h6>
              </div>
              <div class="modern-card-body">
                <p class="text-muted mb-3">Mevcut company'nin cache'ini temizler.</p>
                <button class="modern-btn modern-btn-warning w-100" (click)="clearCompanyCache()">
                  <i class="fas fa-broom me-1"></i>
                  Company Cache Temizle
                </button>
              </div>
            </div>
          </div>

          <div class="col-md-4 mb-3">
            <div class="modern-card">
              <div class="modern-card-header">
                <h6 class="mb-0">
                  <i class="fas fa-sync-alt me-2 text-info"></i>
                  Cache Yenile
                </h6>
              </div>
              <div class="modern-card-body">
                <p class="text-muted mb-3">Cache verilerini yeniden yükler.</p>
                <button class="modern-btn modern-btn-info w-100" (click)="refreshCache()">
                  <i class="fas fa-sync-alt me-1"></i>
                  Yenile
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Pattern-based Operations -->
        <div class="modern-card mb-4">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-filter me-2"></i>
              Pattern Bazlı İşlemler
            </h5>
          </div>
          <div class="modern-card-body">
            <div class="row">
              <div class="col-md-8">
                <div class="input-group">
                  <span class="input-group-text"><i class="fas fa-search"></i></span>
                  <input type="text" class="modern-form-control" [(ngModel)]="selectedPattern"
                         placeholder="Pattern girin (örn: member:*, payment:*)">
                  <button class="modern-btn modern-btn-danger" (click)="clearCacheByPattern()" [disabled]="!selectedPattern.trim()">
                    <i class="fas fa-trash me-1"></i>
                    Pattern Temizle
                  </button>
                </div>
              </div>
              <div class="col-md-4">
                <select class="modern-form-control" [(ngModel)]="selectedPattern">
                  <option value="">Hazır Pattern Seç</option>
                  <option *ngFor="let pattern of getPredefinedPatternKeys()" [value]="predefinedPatterns[pattern]">{{ pattern }}</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Debug Area -->
        <div class="modern-card">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-bug me-2"></i>
              Debug Alanı
            </h5>
          </div>
          <div class="modern-card-body">
            <div class="row">
              <div class="col-md-8">
                <div class="input-group mb-3">
                  <span class="input-group-text"><i class="fas fa-key"></i></span>
                  <input type="text" class="modern-form-control" [(ngModel)]="selectedKey"
                         placeholder="Cache key girin">
                  <button class="modern-btn modern-btn-primary" (click)="getCacheKeyValue()" [disabled]="!selectedKey.trim()">
                    <i class="fas fa-eye me-1"></i>
                    Değeri Görüntüle
                  </button>
                </div>
              </div>
            </div>

            <!-- Key Value Display -->
            <div *ngIf="keyValue" class="mt-3">
              <h6>Cache Değeri:</h6>
              <pre class="bg-light p-3 rounded"><code>{{ formatJson(keyValue) }}</code></pre>
            </div>
          </div>
        </div>
      </div>

      <!-- Warmup Tab -->
      <div *ngIf="activeTab === 'warmup'" class="fade-in">
        <!-- Quick Warmup Actions -->
        <div class="row mb-4">
          <div class="col-md-4 mb-3">
            <div class="modern-card">
              <div class="modern-card-header">
                <h6 class="mb-0">
                  <i class="fas fa-users me-2 text-success"></i>
                  Üye Cache Warmup
                </h6>
              </div>
              <div class="modern-card-body">
                <p class="text-muted mb-3">Aktif üyelerin cache'ini önceden yükler.</p>
                <button class="modern-btn modern-btn-success w-100" (click)="warmupCache()">
                  <i class="fas fa-fire me-1"></i>
                  Üye Cache Warmup
                </button>
              </div>
            </div>
          </div>

          <div class="col-md-4 mb-3">
            <div class="modern-card">
              <div class="modern-card-header">
                <h6 class="mb-0">
                  <i class="fas fa-credit-card me-2 text-info"></i>
                  Ödeme Cache Warmup
                </h6>
              </div>
              <div class="modern-card-body">
                <p class="text-muted mb-3">Son ödeme kayıtlarını cache'e yükler.</p>
                <button class="modern-btn modern-btn-info w-100" (click)="warmupCache()">
                  <i class="fas fa-fire me-1"></i>
                  Ödeme Cache Warmup
                </button>
              </div>
            </div>
          </div>

          <div class="col-md-4 mb-3">
            <div class="modern-card">
              <div class="modern-card-header">
                <h6 class="mb-0">
                  <i class="fas fa-dumbbell me-2 text-warning"></i>
                  Antrenör Cache Warmup
                </h6>
              </div>
              <div class="modern-card-body">
                <p class="text-muted mb-3">Antrenör bilgilerini cache'e yükler.</p>
                <button class="modern-btn modern-btn-warning w-100" (click)="warmupCache()">
                  <i class="fas fa-fire me-1"></i>
                  Antrenör Cache Warmup
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Custom Warmup -->
        <div class="modern-card mb-4">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-cogs me-2"></i>
              Özel Warmup İşlemleri
            </h5>
          </div>
          <div class="modern-card-body">
            <div class="row">
              <div class="col-md-6">
                <label class="form-label">Entity Türü</label>
                <select class="modern-form-control" [(ngModel)]="selectedPattern">
                  <option value="">Entity seçin</option>
                  <option value="members">Üyeler</option>
                  <option value="payments">Ödemeler</option>
                  <option value="trainers">Antrenörler</option>
                  <option value="packages">Paketler</option>
                  <option value="entries">Giriş Kayıtları</option>
                  <option value="all">Tümü</option>
                </select>
              </div>
              <div class="col-md-6">
                <label class="form-label">&nbsp;</label>
                <button class="modern-btn modern-btn-primary w-100"
                        (click)="warmupCache()"
                        [disabled]="!selectedPattern">
                  <i class="fas fa-rocket me-1"></i>
                  Warmup Başlat
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Warmup Status -->
        <div class="modern-card" *ngIf="warmupStatus">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-chart-line me-2"></i>
              Warmup Durumu
            </h5>
          </div>
          <div class="modern-card-body">
            <div class="row mb-3">
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="modern-stats-card bg-success">
                  <div class="modern-stats-icon">
                    <i class="fas fa-check"></i>
                  </div>
                  <div class="modern-stats-info">
                    <h6 class="text-white mb-1">Tamamlanan</h6>
                    <h4 class="text-white mb-0">{{ warmupStatus.completedItems | number }}</h4>
                    <small class="text-light">Items</small>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="modern-stats-card bg-info">
                  <div class="modern-stats-icon">
                    <i class="fas fa-list"></i>
                  </div>
                  <div class="modern-stats-info">
                    <h6 class="text-white mb-1">Toplam</h6>
                    <h4 class="text-white mb-0">{{ warmupStatus.totalItems | number }}</h4>
                    <small class="text-light">Items</small>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="modern-stats-card bg-warning">
                  <div class="modern-stats-icon">
                    <i class="fas fa-clock"></i>
                  </div>
                  <div class="modern-stats-info">
                    <h6 class="text-white mb-1">Süre</h6>
                    <h4 class="text-white mb-0">{{ warmupStatus.duration | number }}ms</h4>
                    <small class="text-light">Duration</small>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="modern-stats-card" [ngClass]="warmupStatus.isCompleted ? 'bg-success' : 'bg-primary'">
                  <div class="modern-stats-icon">
                    <i class="fas" [ngClass]="warmupStatus.isCompleted ? 'fa-check-circle' : 'fa-spinner fa-spin'"></i>
                  </div>
                  <div class="modern-stats-info">
                    <h6 class="text-white mb-1">Durum</h6>
                    <h4 class="text-white mb-0">{{ warmupStatus.isCompleted ? 'Tamamlandı' : 'Devam Ediyor' }}</h4>
                    <small class="text-light">Status</small>
                  </div>
                </div>
              </div>
            </div>

            <!-- Progress Bar -->
            <div class="progress" style="height: 10px;">
              <div class="progress-bar progress-bar-striped"
                   [class.progress-bar-animated]="!warmupStatus.isCompleted"
                   [style.width.%]="(warmupStatus.completedItems / warmupStatus.totalItems) * 100">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
      <div class="col-12">
        <div class="d-flex justify-content-end gap-2">
          <button class="modern-btn modern-btn-outline-secondary" (click)="refreshData()">
            <i class="fas fa-sync-alt me-1"></i>
            Yenile
          </button>
          <button class="modern-btn modern-btn-success" (click)="testCache()">
            <i class="fas fa-check-circle me-1"></i>
            Test Cache
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

