import { Component, OnInit } from '@angular/core';
import {
  CacheAdminService,
  CacheStatistics,
  CacheHealth,
  CacheKeysResponse,
  CacheKeyDetail,
  TenantCacheDetails,
  CacheWarmupRequest,
  CacheWarmupResult,
  CacheClearResult
} from '../../services/cache-admin.service';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from '../../services/auth.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-cache-admin',
  templateUrl: './cache-admin.component.html',
  styleUrls: ['./cache-admin.component.css'],
  standalone: false
})
export class CacheAdminComponent implements OnInit {
  // Backend API ile uyumlu özellikler
  statistics: CacheStatistics | null = null;
  healthInfo: CacheHealth | null = null;
  cacheKeys: CacheKeysResponse | null = null;
  tenantDetails: TenantCacheDetails | null = null;

  // UI kontrol
  isLoading = false;
  activeTab = 'overview'; // overview, keys, management, warmup

  // Arama ve filtreleme
  searchPattern = '';
  currentPage = 1;
  pageSize = 50;

  // Cache yönetimi
  selectedPattern = '';
  selectedTenantId: number | null = null;
  warmupStatus: any = null;
  warmupConfig: CacheWarmupRequest = {
    warmupMembers: true,
    warmupPayments: true,
    warmupMemberships: true,
    warmupUsers: false,
    warmupCompanySettings: true
  };

  // Predefined patterns
  predefinedPatterns: { [key: string]: string } = {
    'Tüm Üyeler': 'member:*',
    'Tüm Ödemeler': 'payment:*',
    'Tüm Üyelikler': 'membership:*',
    'Tüm Kullanıcılar': 'user:*',
    'Şirket Ayarları': 'company:*',
    'Tüm Cache': '*'
  };

  quickWarmupConfigs: { [key: string]: CacheWarmupRequest } = {
    'Temel': {
      warmupMembers: true,
      warmupPayments: false,
      warmupMemberships: true,
      warmupUsers: false,
      warmupCompanySettings: true
    },
    'Tam': {
      warmupMembers: true,
      warmupPayments: true,
      warmupMemberships: true,
      warmupUsers: true,
      warmupCompanySettings: true
    },
    'Sadece Üyeler': {
      warmupMembers: true,
      warmupPayments: false,
      warmupMemberships: false,
      warmupUsers: false,
      warmupCompanySettings: false
    }
  };

  // Debug
  selectedKey = '';
  keyValue: any = null;

  // Math helper for template
  Math = Math;

  constructor(
    private cacheAdminService: CacheAdminService,
    private toastrService: ToastrService,
    private authService: AuthService,
    private router: Router
  ) {
    // Predefined patterns ve configs'i initialize et
    this.predefinedPatterns = this.cacheAdminService.getCachePatterns();
    this.quickWarmupConfigs = this.cacheAdminService.getQuickWarmupConfigs();
  }

  ngOnInit(): void {
    // Yetki kontrolü
    if (!this.authService.hasRole('owner')) {
      this.toastrService.error('Bu sayfaya erişim yetkiniz bulunmamaktadır.');
      this.router.navigate(['/unauthorized']);
      return;
    }

    this.loadInitialData();
  }

  /**
   * İlk yükleme - temel cache bilgilerini getir
   */
  loadInitialData(): void {
    this.isLoading = true;

    Promise.all([
      this.loadStatistics(),
      this.loadHealthInfo(),
      this.loadTenantDetails(),
      this.loadCacheKeys()
    ]).finally(() => {
      this.isLoading = false;
    });
  }

  /**
   * Cache istatistiklerini yükle
   */
  private loadStatistics(): Promise<void> {
    return new Promise((resolve) => {
      this.cacheAdminService.getStatistics().subscribe({
        next: (response) => {
          if (response.success) {
            this.statistics = response.data;
          } else {
            this.toastrService.error(response.message);
          }
          resolve();
        },
        error: (error) => {
          this.toastrService.error('Cache istatistikleri yüklenirken hata oluştu');
          resolve();
        }
      });
    });
  }

  /**
   * Cache sağlık durumunu yükle
   */
  private loadHealthInfo(): Promise<void> {
    return new Promise((resolve) => {
      this.cacheAdminService.getHealthInfo().subscribe({
        next: (response) => {
          if (response.success) {
            this.healthInfo = response.data;
          } else {
            this.toastrService.error(response.message);
          }
          resolve();
        },
        error: (error) => {
          this.toastrService.error('Cache sağlık bilgileri yüklenirken hata oluştu');
          resolve();
        }
      });
    });
  }

  /**
   * Company cache detaylarını yükle
   */
  private loadTenantDetails(): Promise<void> {
    return new Promise((resolve) => {
      this.cacheAdminService.getTenantCacheDetails().subscribe({
        next: (response) => {
          if (response.success) {
            this.tenantDetails = response.data;
          } else {
            this.toastrService.error(response.message);
          }
          resolve();
        },
        error: (error) => {
          this.toastrService.error('Company cache detayları yüklenirken hata oluştu');
          resolve();
        }
      });
    });
  }

  /**
   * Cache key'lerini yükle (pagination ile)
   */
  loadCacheKeys(): Promise<void> {
    return new Promise((resolve) => {
      this.cacheAdminService.getCompanyCacheKeys(this.currentPage, this.pageSize).subscribe({
        next: (response) => {
          if (response.success) {
            this.cacheKeys = response.data;
          } else {
            this.toastrService.error(response.message);
          }
          resolve();
        },
        error: (error) => {
          this.toastrService.error('Cache key\'leri yüklenirken hata oluştu');
          resolve();
        }
      });
    });
  }

  /**
   * Pattern ile cache key'lerini ara
   */
  searchKeys(): void {
    if (!this.searchPattern.trim()) {
      this.loadCacheKeys();
      return;
    }

    this.isLoading = true;
    this.cacheAdminService.getKeysByPattern(this.searchPattern, this.currentPage, this.pageSize).subscribe({
      next: (response) => {
        if (response.success) {
          this.cacheKeys = response.data;
          this.toastrService.success(`${response.data.pagination.totalCount} adet key bulundu`);
        } else {
          this.toastrService.error(response.message);
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.toastrService.error('Arama yapılırken hata oluştu');
        this.isLoading = false;
      }
    });
  }

  /**
   * Company'nin tüm cache'ini temizle
   */
  clearTenantCache(): void {
    if (!confirm('Company\'nizin tüm cache verilerini silmek istediğinizden emin misiniz?')) {
      return;
    }

    this.isLoading = true;
    this.cacheAdminService.clearTenantCache().subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success(`${response.data.removedCount} adet cache key silindi`);
          this.loadInitialData();
        } else {
          this.toastrService.error(response.message);
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.toastrService.error('Cache temizlenirken hata oluştu');
        this.isLoading = false;
      }
    });
  }

  /**
   * Belirli pattern'deki cache'leri temizle
   */
  clearCacheByPattern(): void {
    if (!this.selectedPattern.trim()) {
      this.toastrService.warning('Lütfen bir pattern girin');
      return;
    }

    if (!confirm(`"${this.selectedPattern}" pattern'ine uygun cache verilerini silmek istediğinizden emin misiniz?`)) {
      return;
    }

    this.isLoading = true;
    this.cacheAdminService.clearCacheByPattern(this.selectedPattern).subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success(`${response.data.removedCount} adet cache key silindi`);
          this.loadInitialData();
        } else {
          this.toastrService.error(response.message);
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.toastrService.error('Pattern cache temizlenirken hata oluştu');
        this.isLoading = false;
      }
    });
  }

  /**
   * Cache warmup işlemi başlat
   */
  warmupCache(): void {
    if (!confirm('Cache warmup işlemini başlatmak istediğinizden emin misiniz?')) {
      return;
    }

    this.isLoading = true;
    this.cacheAdminService.warmupCache(this.warmupConfig).subscribe({
      next: (response) => {
        if (response.success) {
          const result = response.data;
          this.toastrService.success(`Cache warmup tamamlandı. Süre: ${result.totalDuration}ms`);
          this.loadInitialData();
        } else {
          this.toastrService.error(response.message);
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.toastrService.error('Cache warmup işlemi sırasında hata oluştu');
        this.isLoading = false;
      }
    });
  }

  /**
   * Quick warmup config'i uygula
   */
  applyQuickWarmupConfig(configName: string): void {
    if (this.quickWarmupConfigs[configName]) {
      this.warmupConfig = { ...this.quickWarmupConfigs[configName] };
      this.toastrService.info(`${configName} konfigürasyonu uygulandı`);
    }
  }

  /**
   * Predefined pattern'i uygula
   */
  applyPredefinedPattern(patternName: string): void {
    if (this.predefinedPatterns[patternName]) {
      this.selectedPattern = this.predefinedPatterns[patternName];
      this.toastrService.info(`${patternName} pattern'i uygulandı`);
    }
  }

  /**
   * Belirli cache key'ini sil
   */
  deleteCacheKey(key: string): void {
    if (!confirm(`"${key}" cache key'ini silmek istediğinizden emin misiniz?`)) {
      return;
    }

    this.cacheAdminService.deleteCacheKey(key).subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success(response.data.removed ? 'Cache key silindi' : 'Cache key bulunamadı');
          this.loadCacheKeys();
        } else {
          this.toastrService.error(response.message);
        }
      },
      error: (error) => {
        this.toastrService.error('Cache key silinirken hata oluştu');
      }
    });
  }

  /**
   * Cache key'inin değerini getir (debug)
   */
  getCacheKeyValue(): void {
    if (!this.selectedKey.trim()) {
      this.toastrService.warning('Lütfen bir cache key girin');
      return;
    }

    this.cacheAdminService.getCacheKeyValue(this.selectedKey).subscribe({
      next: (response) => {
        if (response.success) {
          this.keyValue = response.data;
          this.toastrService.success('Cache key değeri getirildi');
        } else {
          this.toastrService.error(response.message);
        }
      },
      error: (error) => {
        this.toastrService.error('Cache key değeri alınırken hata oluştu');
      }
    });
  }

  /**
   * Cache test et (sağlık kontrolü)
   */
  testCache(): void {
    this.loadHealthInfo().then(() => {
      if (this.healthInfo?.status === 'Healthy') {
        this.toastrService.success('Cache bağlantısı başarılı!');
      } else {
        this.toastrService.error('Cache bağlantısında sorun var!');
      }
    });
  }

  /**
   * Verileri yenile
   */
  refreshData(): void {
    this.loadInitialData();
    this.toastrService.info('Veriler yenilendi');
  }

  /**
   * Tab değiştir
   */
  setActiveTab(tab: string): void {
    this.activeTab = tab;

    // Tab değiştiğinde ilgili verileri yükle
    if (tab === 'keys' && !this.cacheKeys) {
      this.loadCacheKeys();
    }
  }

  /**
   * Sayfa değiştir (pagination)
   */
  changePage(page: number): void {
    this.currentPage = page;
    if (this.searchPattern.trim()) {
      this.searchKeys();
    } else {
      this.loadCacheKeys();
    }
  }

  /**
   * Sayfa boyutunu değiştir
   */
  changePageSize(size: number): void {
    this.pageSize = size;
    this.currentPage = 1;
    if (this.searchPattern.trim()) {
      this.searchKeys();
    } else {
      this.loadCacheKeys();
    }
  }

  /**
   * Utility Methods
   */
  formatBytes(bytes: number): string {
    return this.cacheAdminService.formatMemoryUsage(bytes);
  }

  formatTTL(seconds?: number): string {
    return this.cacheAdminService.formatTTL(seconds);
  }

  getHitRatioColor(ratio: number): string {
    if (ratio >= 80) return 'text-success';
    if (ratio >= 60) return 'text-warning';
    return 'text-danger';
  }

  getHealthStatusColor(status: string): string {
    return status === 'Healthy' ? 'text-success' : 'text-danger';
  }

  getMemoryUsageColor(percentage: number): string {
    if (percentage >= 80) return 'text-danger';
    if (percentage >= 60) return 'text-warning';
    return 'text-success';
  }

  /**
   * Helper Methods
   */

  /**
   * Cache key'lerini entity'ye göre grupla
   */
  getGroupedKeys(): { [entity: string]: CacheKeyDetail[] } {
    if (!this.cacheKeys?.keys) return {};
    return this.cacheAdminService.groupKeysByEntity(this.cacheKeys.keys);
  }

  /**
   * Entity'lerin key sayılarını al
   */
  getEntityKeyCounts(): { [entity: string]: number } {
    const grouped = this.getGroupedKeys();
    const counts: { [entity: string]: number } = {};

    Object.keys(grouped).forEach(entity => {
      counts[entity] = grouped[entity].length;
    });

    return counts;
  }

  /**
   * Toplam memory usage'ı hesapla
   */
  getTotalMemoryUsage(): number {
    if (!this.cacheKeys?.keys) return 0;
    return this.cacheKeys.keys.reduce((total, key) => total + key.memoryUsage, 0);
  }

  /**
   * Cache hit ratio'yu hesapla (mock data)
   */
  getCacheHitRatio(): number {
    // Bu değer gerçek implementasyonda backend'den gelecek
    return this.statistics ? 85 : 0;
  }

  /**
   * Predefined pattern'lerin key'lerini al
   */
  getPredefinedPatternKeys(): string[] {
    return Object.keys(this.predefinedPatterns);
  }

  /**
   * Quick warmup config'lerin key'lerini al
   */
  getQuickWarmupConfigKeys(): string[] {
    return Object.keys(this.quickWarmupConfigs);
  }

  /**
   * DateTime formatter
   */
  formatDateTime(dateString?: string): string {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('tr-TR');
  }

  /**
   * JSON formatter (debug için)
   */
  formatJson(obj: any): string {
    return JSON.stringify(obj, null, 2);
  }

  /**
   * Cache key'inin kısa versiyonunu al (UI için)
   */
  getShortKey(key: string, maxLength: number = 50): string {
    if (key.length <= maxLength) return key;
    return key.substring(0, maxLength) + '...';
  }

  /**
   * Additional methods for new template
   */
  clearAllCache(): void {
    if (confirm('Tüm cache verilerini silmek istediğinizden emin misiniz?')) {
      this.isLoading = true;
      this.cacheAdminService.clearAllCache().subscribe({
        next: () => {
          this.toastrService.success('Tüm cache verileri başarıyla temizlendi');
          this.refreshData();
        },
        error: (error) => {
          this.toastrService.error('Cache temizleme işlemi başarısız: ' + error.message);
          this.isLoading = false;
        }
      });
    }
  }

  clearCompanyCache(): void {
    if (confirm('Company cache verilerini silmek istediğinizden emin misiniz?')) {
      this.isLoading = true;
      this.cacheAdminService.clearCompanyCache().subscribe({
        next: () => {
          this.toastrService.success('Company cache verileri başarıyla temizlendi');
          this.refreshData();
        },
        error: (error) => {
          this.toastrService.error('Company cache temizleme işlemi başarısız: ' + error.message);
          this.isLoading = false;
        }
      });
    }
  }

  refreshCache(): void {
    this.refreshData();
  }
}
