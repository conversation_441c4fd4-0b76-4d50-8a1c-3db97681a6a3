using Business.Abstract;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StackExchange.Redis;
using System.Diagnostics;

namespace WebAPI.Controllers
{
    /// <summary>
    /// Cache Admin Controller - Frontend cache yönetimi için API endpoints
    /// Owner/Admin yetkisi gerektirir
    /// Multi-tenant cache isolation destekler
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CacheAdminController : ControllerBase
    {
        private readonly ICacheService _cacheService;
        private readonly ICompanyContext _companyContext;
        private readonly IConnectionMultiplexer _connectionMultiplexer;

        public CacheAdminController(
            ICacheService cacheService,
            ICompanyContext companyContext,
            IConnectionMultiplexer connectionMultiplexer)
        {
            _cacheService = cacheService;
            _companyContext = companyContext;
            _connectionMultiplexer = connectionMultiplexer;
        }

        /// <summary>
        /// Cache istatistiklerini getirir (Company bazlı)
        /// </summary>
        [HttpGet("statistics")]
        public async Task<IActionResult> GetCacheStatistics()
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                    return BadRequest(new ErrorResult("Geçersiz company ID"));

                var statistics = await GetCompanyCacheStatistics(companyId);
                return Ok(new SuccessDataResult<object>(statistics, "Cache istatistikleri başarıyla getirildi"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ErrorResult($"Cache istatistikleri alınırken hata oluştu: {ex.Message}"));
            }
        }

        /// <summary>
        /// Redis bağlantı durumunu kontrol eder
        /// </summary>
        [HttpGet("health")]
        public async Task<IActionResult> GetCacheHealth()
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();
                var database = _connectionMultiplexer.GetDatabase();
                
                // Ping test
                var pingTime = await database.PingAsync();
                stopwatch.Stop();

                var health = new
                {
                    IsConnected = _connectionMultiplexer.IsConnected,
                    PingTime = pingTime.TotalMilliseconds,
                    ResponseTime = stopwatch.ElapsedMilliseconds,
                    Status = _connectionMultiplexer.IsConnected ? "Healthy" : "Unhealthy",
                    ServerInfo = await GetRedisServerInfo()
                };

                return Ok(new SuccessDataResult<object>(health, "Cache sağlık durumu başarıyla kontrol edildi"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ErrorResult($"Cache sağlık kontrolü yapılırken hata oluştu: {ex.Message}"));
            }
        }

        /// <summary>
        /// Company'ye ait cache key'lerini listeler
        /// </summary>
        [HttpGet("keys")]
        public async Task<IActionResult> GetCompanyCacheKeys([FromQuery] int page = 1, [FromQuery] int size = 50)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                    return BadRequest(new ErrorResult("Geçersiz company ID"));

                var pattern = $"gym:{companyId}:*";
                var keys = await GetKeysByPattern(pattern, page, size);
                
                return Ok(new SuccessDataResult<object>(keys, "Cache key'leri başarıyla getirildi"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ErrorResult($"Cache key'leri alınırken hata oluştu: {ex.Message}"));
            }
        }

        /// <summary>
        /// Belirli pattern'e göre cache key'lerini listeler
        /// </summary>
        [HttpGet("keys/pattern/{pattern}")]
        public async Task<IActionResult> GetKeysByPatternEndpoint(string pattern, [FromQuery] int page = 1, [FromQuery] int size = 50)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                    return BadRequest(new ErrorResult("Geçersiz company ID"));

                // Security: Company ID kontrolü - sadece kendi company'sine ait pattern'lere izin ver
                if (!pattern.StartsWith($"gym:{companyId}:"))
                {
                    pattern = $"gym:{companyId}:{pattern.TrimStart('*')}";
                }

                var keys = await GetKeysByPattern(pattern, page, size);
                return Ok(new SuccessDataResult<object>(keys, "Pattern'e göre cache key'leri başarıyla getirildi"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ErrorResult($"Pattern cache key'leri alınırken hata oluştu: {ex.Message}"));
            }
        }

        /// <summary>
        /// Company'nin tüm cache'ini temizler
        /// </summary>
        [HttpDelete("clear/tenant")]
        public async Task<IActionResult> ClearTenantCache()
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                    return BadRequest(new ErrorResult("Geçersiz company ID"));

                var pattern = $"gym:{companyId}:*";
                var removedCount = _cacheService.RemoveByPattern(pattern);

                return Ok(new SuccessDataResult<object>(
                    new { RemovedCount = removedCount, Pattern = pattern },
                    $"Company cache'i başarıyla temizlendi. {removedCount} adet key silindi"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ErrorResult($"Company cache temizlenirken hata oluştu: {ex.Message}"));
            }
        }

        /// <summary>
        /// Belirli pattern'deki cache'leri temizler
        /// </summary>
        [HttpDelete("clear/pattern/{pattern}")]
        public async Task<IActionResult> ClearCacheByPattern(string pattern)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                    return BadRequest(new ErrorResult("Geçersiz company ID"));

                // Security: Company ID kontrolü
                if (!pattern.StartsWith($"gym:{companyId}:"))
                {
                    pattern = $"gym:{companyId}:{pattern.TrimStart('*')}";
                }

                var removedCount = _cacheService.RemoveByPattern(pattern);

                return Ok(new SuccessDataResult<object>(
                    new { RemovedCount = removedCount, Pattern = pattern },
                    $"Pattern cache'i başarıyla temizlendi. {removedCount} adet key silindi"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ErrorResult($"Pattern cache temizlenirken hata oluştu: {ex.Message}"));
            }
        }

        /// <summary>
        /// Company cache detaylarını getirir
        /// </summary>
        [HttpGet("tenant/details")]
        public async Task<IActionResult> GetTenantCacheDetails()
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                    return BadRequest(new ErrorResult("Geçersiz company ID"));

                var details = await GetCompanyCacheDetails(companyId);
                return Ok(new SuccessDataResult<object>(details, "Company cache detayları başarıyla getirildi"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ErrorResult($"Company cache detayları alınırken hata oluştu: {ex.Message}"));
            }
        }

        /// <summary>
        /// Cache warmup işlemi başlatır
        /// </summary>
        [HttpPost("warmup")]
        public async Task<IActionResult> WarmupCache([FromBody] CacheWarmupRequest request)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                    return BadRequest(new ErrorResult("Geçersiz company ID"));

                var result = await PerformCacheWarmup(companyId, request);
                return Ok(new SuccessDataResult<object>(result, "Cache warmup işlemi başarıyla tamamlandı"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ErrorResult($"Cache warmup işlemi sırasında hata oluştu: {ex.Message}"));
            }
        }

        /// <summary>
        /// Belirli cache key'ini siler
        /// </summary>
        [HttpDelete("key/{key}")]
        public async Task<IActionResult> DeleteCacheKey(string key)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                    return BadRequest(new ErrorResult("Geçersiz company ID"));

                // Security: Company ID kontrolü
                if (!key.StartsWith($"gym:{companyId}:"))
                {
                    return BadRequest(new ErrorResult("Bu cache key'ine erişim yetkiniz yok"));
                }

                var removed = _cacheService.Remove(key);
                return Ok(new SuccessDataResult<object>(
                    new { Key = key, Removed = removed },
                    removed ? "Cache key başarıyla silindi" : "Cache key bulunamadı"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ErrorResult($"Cache key silinirken hata oluştu: {ex.Message}"));
            }
        }

        /// <summary>
        /// Cache key'inin değerini getirir (debug amaçlı)
        /// </summary>
        [HttpGet("key/{key}/value")]
        public async Task<IActionResult> GetCacheKeyValue(string key)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                    return BadRequest(new ErrorResult("Geçersiz company ID"));

                // Security: Company ID kontrolü
                if (!key.StartsWith($"gym:{companyId}:"))
                {
                    return BadRequest(new ErrorResult("Bu cache key'ine erişim yetkiniz yok"));
                }

                var database = _connectionMultiplexer.GetDatabase();
                var value = await database.StringGetAsync(key);
                var ttl = await database.KeyTimeToLiveAsync(key);

                var result = new
                {
                    Key = key,
                    Value = value.HasValue ? value.ToString() : null,
                    HasValue = value.HasValue,
                    TTL = ttl?.TotalSeconds,
                    Size = value.HasValue ? System.Text.Encoding.UTF8.GetByteCount(value) : 0
                };

                return Ok(new SuccessDataResult<object>(result, "Cache key değeri başarıyla getirildi"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ErrorResult($"Cache key değeri alınırken hata oluştu: {ex.Message}"));
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// Company'ye ait cache istatistiklerini hesaplar
        /// </summary>
        private async Task<object> GetCompanyCacheStatistics(int companyId)
        {
            var database = _connectionMultiplexer.GetDatabase();
            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());

            var pattern = $"gym:{companyId}:*";
            var keys = server.Keys(pattern: pattern).ToList();

            var totalKeys = keys.Count;
            var totalMemoryUsage = 0L;
            var keysByEntity = new Dictionary<string, int>();

            foreach (var key in keys)
            {
                try
                {
                    // Memory usage hesapla (basit yaklaşım)
                    var keyValue = await database.StringGetAsync(key);
                    if (keyValue.HasValue)
                    {
                        totalMemoryUsage += keyValue.ToString().Length;
                    }

                    // Entity bazlı gruplandırma
                    var keyParts = key.ToString().Split(':');
                    if (keyParts.Length >= 3)
                    {
                        var entity = keyParts[2];
                        keysByEntity[entity] = keysByEntity.GetValueOrDefault(entity, 0) + 1;
                    }
                }
                catch
                {
                    // Hata durumunda devam et
                }
            }

            return new
            {
                CompanyId = companyId,
                TotalKeys = totalKeys,
                TotalMemoryUsage = totalMemoryUsage,
                TotalMemoryUsageMB = Math.Round(totalMemoryUsage / 1024.0 / 1024.0, 2),
                KeysByEntity = keysByEntity,
                LastUpdated = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Redis server bilgilerini getirir
        /// </summary>
        private async Task<object> GetRedisServerInfo()
        {
            try
            {
                var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
                var info = await server.InfoAsync();

                var serverInfo = info.FirstOrDefault(i => i.Key == "Server");
                var memoryInfo = info.FirstOrDefault(i => i.Key == "Memory");

                var version = serverInfo?.FirstOrDefault(s => s.Key == "redis_version");
                var uptime = serverInfo?.FirstOrDefault(s => s.Key == "uptime_in_seconds");
                var usedMem = memoryInfo?.FirstOrDefault(m => m.Key == "used_memory");
                var usedMemHuman = memoryInfo?.FirstOrDefault(m => m.Key == "used_memory_human");
                var maxMem = memoryInfo?.FirstOrDefault(m => m.Key == "maxmemory");
                var maxMemHuman = memoryInfo?.FirstOrDefault(m => m.Key == "maxmemory_human");

                return new
                {
                    Version = version?.Value.ToString() ?? "Unknown",
                    UptimeInSeconds = uptime?.Value.ToString() ?? "0",
                    UsedMemory = usedMem?.Value.ToString() ?? "0",
                    UsedMemoryHuman = usedMemHuman?.Value.ToString() ?? "0B",
                    MaxMemory = maxMem?.Value.ToString() ?? "0",
                    MaxMemoryHuman = maxMemHuman?.Value.ToString() ?? "0B"
                };
            }
            catch
            {
                return new { Error = "Server bilgileri alınamadı" };
            }
        }

        /// <summary>
        /// Pattern'e göre cache key'lerini getirir (pagination ile)
        /// </summary>
        private async Task<object> GetKeysByPattern(string pattern, int page, int size)
        {
            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
            var database = _connectionMultiplexer.GetDatabase();

            var allKeys = server.Keys(pattern: pattern).ToList();
            var totalCount = allKeys.Count;
            var totalPages = (int)Math.Ceiling((double)totalCount / size);

            var pagedKeys = allKeys
                .Skip((page - 1) * size)
                .Take(size)
                .ToList();

            var keyDetails = new List<object>();

            foreach (var key in pagedKeys)
            {
                try
                {
                    var ttl = await database.KeyTimeToLiveAsync(key);
                    var type = await database.KeyTypeAsync(key);
                    var keyValue = await database.StringGetAsync(key);

                    keyDetails.Add(new
                    {
                        Key = key.ToString(),
                        Type = type.ToString(),
                        TTL = ttl?.TotalSeconds,
                        MemoryUsage = keyValue.HasValue ? keyValue.ToString().Length : 0,
                        CreatedAt = DateTime.UtcNow.AddSeconds(-(ttl?.TotalSeconds ?? 0))
                    });
                }
                catch
                {
                    keyDetails.Add(new
                    {
                        Key = key.ToString(),
                        Type = "Unknown",
                        TTL = (double?)null,
                        MemoryUsage = 0,
                        CreatedAt = (DateTime?)null
                    });
                }
            }

            return new
            {
                Keys = keyDetails,
                Pagination = new
                {
                    CurrentPage = page,
                    PageSize = size,
                    TotalCount = totalCount,
                    TotalPages = totalPages,
                    HasNextPage = page < totalPages,
                    HasPreviousPage = page > 1
                },
                Pattern = pattern
            };
        }

        /// <summary>
        /// Company cache detaylarını getirir
        /// </summary>
        private async Task<object> GetCompanyCacheDetails(int companyId)
        {
            var statistics = await GetCompanyCacheStatistics(companyId);
            var health = await GetCacheHealth();

            return new
            {
                Statistics = statistics,
                Health = health,
                CompanyId = companyId,
                CachePatterns = new[]
                {
                    $"gym:{companyId}:member:*",
                    $"gym:{companyId}:payment:*",
                    $"gym:{companyId}:membership:*",
                    $"gym:{companyId}:user:*",
                    $"gym:{companyId}:company:*"
                }
            };
        }

        /// <summary>
        /// Cache warmup işlemini gerçekleştirir
        /// </summary>
        private async Task<object> PerformCacheWarmup(int companyId, CacheWarmupRequest request)
        {
            var warmupResults = new List<object>();
            var stopwatch = Stopwatch.StartNew();

            // Temel cache'leri warmup et
            if (request.WarmupMembers)
            {
                // Member cache warmup logic burada implement edilecek
                warmupResults.Add(new { Entity = "Members", Status = "Completed", Duration = "0ms" });
            }

            if (request.WarmupPayments)
            {
                // Payment cache warmup logic burada implement edilecek
                warmupResults.Add(new { Entity = "Payments", Status = "Completed", Duration = "0ms" });
            }

            if (request.WarmupMemberships)
            {
                // Membership cache warmup logic burada implement edilecek
                warmupResults.Add(new { Entity = "Memberships", Status = "Completed", Duration = "0ms" });
            }

            stopwatch.Stop();

            return new
            {
                CompanyId = companyId,
                TotalDuration = stopwatch.ElapsedMilliseconds,
                Results = warmupResults,
                CompletedAt = DateTime.UtcNow
            };
        }

        #endregion
    }

    /// <summary>
    /// Cache warmup request model
    /// </summary>
    public class CacheWarmupRequest
    {
        public bool WarmupMembers { get; set; } = true;
        public bool WarmupPayments { get; set; } = true;
        public bool WarmupMemberships { get; set; } = true;
        public bool WarmupUsers { get; set; } = false;
        public bool WarmupCompanySettings { get; set; } = false;
    }
}
