
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 41548, hash: 'a758cd7c973a6834d87ca3b28ecfd4915cbef8fa7cc5740ff8414e90545b302a', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 21716, hash: '3ed3ae6fca565910c0297121511b9b649156c80e4b07cf50219dfe21742fe74e', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-V3UH3NNJ.css': {size: 302582, hash: 'KVkHM28HPpc', text: () => import('./assets-chunks/styles-V3UH3NNJ_css.mjs').then(m => m.default)}
  },
};
